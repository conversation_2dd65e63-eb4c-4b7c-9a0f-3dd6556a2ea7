{"name": "ai-saas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@clerk/nextjs": "^4.23.5", "@hookform/resolvers": "^3.3.1", "@prisma/client": "^5.3.1", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-slot": "^1.0.2", "@types/node": "20.6.0", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "axios": "^1.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "crisp-sdk-web": "^1.0.21", "eslint": "8.49.0", "eslint-config-next": "13.4.19", "lucide-react": "^0.277.0", "next": "13.4.19", "openai": "^3.3.0", "postcss": "8.4.29", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.46.1", "react-hot-toast": "^2.4.1", "react-markdown": "^8.0.7", "replicate": "^0.18.0", "stripe": "^13.6.0", "tailwind-merge": "^1.14.0", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "typewriter-effect": "^2.20.1", "zod": "^3.22.2", "zustand": "^4.4.1"}, "devDependencies": {"prisma": "^5.0.0"}}